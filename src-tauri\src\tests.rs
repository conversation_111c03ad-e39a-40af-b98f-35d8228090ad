#[cfg(test)]
mod tests {
    use super::game::{Game, Direction};

    #[test]
    fn test_new_game() {
        let game = Game::new();
        
        // 检查初始状态
        assert_eq!(game.score, 0);
        assert_eq!(game.game_over, false);
        assert_eq!(game.won, false);
        
        // 检查是否有两个初始数字块
        let mut count = 0;
        for row in &game.board {
            for &cell in row {
                if cell != 0 {
                    count += 1;
                    assert!(cell == 2 || cell == 4);
                }
            }
        }
        assert_eq!(count, 2);
    }

    #[test]
    fn test_move_left() {
        let mut game = Game::new();
        
        // 设置一个测试棋盘
        game.board = [
            [2, 2, 0, 0],
            [4, 0, 4, 0],
            [0, 0, 0, 0],
            [2, 4, 2, 4]
        ];
        game.score = 0;
        
        game.move_tiles(Direction::Left);
        
        // 检查第一行：2 + 2 = 4
        assert_eq!(game.board[0][0], 4);
        assert_eq!(game.board[0][1], 0);
        assert_eq!(game.board[0][2], 0);
        assert_eq!(game.board[0][3], 0);
        
        // 检查第二行：4 + 4 = 8
        assert_eq!(game.board[1][0], 8);
        assert_eq!(game.board[1][1], 0);
        assert_eq!(game.board[1][2], 0);
        assert_eq!(game.board[1][3], 0);
        
        // 检查第四行：2, 4, 2, 4 -> 2, 4, 2, 4 (无合并)
        assert_eq!(game.board[3][0], 2);
        assert_eq!(game.board[3][1], 4);
        assert_eq!(game.board[3][2], 2);
        assert_eq!(game.board[3][3], 4);
        
        // 检查得分
        assert_eq!(game.score, 12); // 4 + 8 = 12
    }

    #[test]
    fn test_move_right() {
        let mut game = Game::new();
        
        // 设置一个测试棋盘
        game.board = [
            [0, 0, 2, 2],
            [0, 4, 0, 4],
            [0, 0, 0, 0],
            [2, 4, 2, 4]
        ];
        game.score = 0;
        
        game.move_tiles(Direction::Right);
        
        // 检查第一行：2 + 2 = 4 (在右边)
        assert_eq!(game.board[0][3], 4);
        assert_eq!(game.board[0][2], 0);
        assert_eq!(game.board[0][1], 0);
        assert_eq!(game.board[0][0], 0);
        
        // 检查第二行：4 + 4 = 8 (在右边)
        assert_eq!(game.board[1][3], 8);
        assert_eq!(game.board[1][2], 0);
        assert_eq!(game.board[1][1], 0);
        assert_eq!(game.board[1][0], 0);
    }

    #[test]
    fn test_game_over_detection() {
        let mut game = Game::new();
        
        // 设置一个满棋盘且无法移动的状态
        game.board = [
            [2, 4, 2, 4],
            [4, 2, 4, 2],
            [2, 4, 2, 4],
            [4, 2, 4, 2]
        ];
        
        game.check_game_state();
        assert_eq!(game.game_over, true);
    }

    #[test]
    fn test_win_condition() {
        let mut game = Game::new();
        
        // 设置一个包含2048的棋盘
        game.board = [
            [1024, 1024, 0, 0],
            [0, 0, 0, 0],
            [0, 0, 0, 0],
            [0, 0, 0, 0]
        ];
        game.score = 0;
        
        game.move_tiles(Direction::Left);
        
        // 检查是否达到胜利条件
        assert_eq!(game.won, true);
        assert_eq!(game.board[0][0], 2048);
    }
}
